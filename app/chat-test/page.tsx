"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageCircle, CheckCircle, AlertCircle } from 'lucide-react';

export default function ChatTestPage() {
  const [testResults, setTestResults] = useState<{
    apiTest: 'pending' | 'success' | 'error';
    message?: string;
  }>({
    apiTest: 'pending'
  });

  const testChatAPI = async () => {
    try {
      setTestResults({ apiTest: 'pending' });
      
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'What is Fuse.vip?',
          conversationHistory: []
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setTestResults({
          apiTest: 'success',
          message: data.message
        });
      } else {
        setTestResults({
          apiTest: 'error',
          message: data.error || 'Unknown error'
        });
      }
    } catch (error) {
      setTestResults({
        apiTest: 'error',
        message: error instanceof Error ? error.message : 'Network error'
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Chat Feature Test</h1>
        <p className="text-gray-600">
          Test the Perplexity AI integration and chat functionality for Fuse.vip
        </p>
      </div>

      <div className="grid gap-6">
        {/* API Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Chat API Test
            </CardTitle>
            <CardDescription>
              Test the /api/chat endpoint with a sample question about Fuse.vip
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testChatAPI}
              disabled={testResults.apiTest === 'pending'}
              className="bg-[#3A56FF] hover:bg-[#3A56FF]/90"
            >
              {testResults.apiTest === 'pending' ? 'Testing...' : 'Test Chat API'}
            </Button>

            {testResults.apiTest !== 'pending' && (
              <div className={`p-4 rounded-lg border ${
                testResults.apiTest === 'success' 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center gap-2 mb-2">
                  {testResults.apiTest === 'success' ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-600" />
                  )}
                  <span className={`font-medium ${
                    testResults.apiTest === 'success' ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {testResults.apiTest === 'success' ? 'Success!' : 'Error'}
                  </span>
                </div>
                <div className={`text-sm ${
                  testResults.apiTest === 'success' ? 'text-green-700' : 'text-red-700'
                }`}>
                  {testResults.message}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Chat Widget Info */}
        <Card>
          <CardHeader>
            <CardTitle>Chat Widget</CardTitle>
            <CardDescription>
              The chat widget is now available on all pages via the floating button in the bottom-right corner
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>Floating chat button added to all pages</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>Perplexity AI integration with Fuse.vip context</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>Rate limiting (10 requests per minute per IP)</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>User authentication integration</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>Responsive design matching your brand</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Configuration Info */}
        <Card>
          <CardHeader>
            <CardTitle>Configuration</CardTitle>
            <CardDescription>
              Current setup and configuration details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>Perplexity API Key:</strong> {process.env.NEXT_PERPLEXITY_API_KEY ? 'Configured' : 'Not configured (server-side only)'}
              </div>
              <div>
                <strong>Model:</strong> llama-3.1-sonar-small-128k-chat
              </div>
              <div>
                <strong>Search Domain:</strong> fuse.vip
              </div>
              <div>
                <strong>Rate Limit:</strong> 10 requests per minute per IP
              </div>
              <div>
                <strong>Context:</strong> Trained on Fuse.vip business model, token benefits, and features
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
