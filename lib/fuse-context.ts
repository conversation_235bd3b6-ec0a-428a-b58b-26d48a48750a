/**
 * Fuse.vip Knowledge Base for AI Training
 * This file contains comprehensive information about Fuse.vip to train the AI chat model
 */

export const FUSE_CONTEXT = {
  company: {
    name: "Fuse.vip",
    tagline: "Loyalty Reimagined. Commerce Reconnected!",
    mission: "To empower businesses with innovative loyalty solutions that drive engagement, enhance customer experiences, and create lasting value through blockchain technology.",
    description: "Fuse.vip provides innovative loyalty solutions and digital transformation for businesses across various industries, pioneering the integration of blockchain technology with traditional loyalty programs.",
    website: "https://www.fuse.vip/",
    contact: {
      email: "<EMAIL>",
      company: "Fuse Vip LLC"
    },
    social: {
      twitter: "@fuserewards",
      discord: "https://discord.gg/n9d7PEbm"
    }
  },

  fuseToken: {
    name: "Fuse.vip Token ($FUSE)",
    launchDate: "Recently launched",
    blockchain: "XRP Ledger",
    description: "A native digital asset built on the XRP Ledger, engineered to redefine how businesses cultivate customer loyalty and long-term engagement.",
    benefits: [
      "Seamless customer loyalty program integration",
      "Automated reward distribution through XRP WebHooks", 
      "Enhanced customer engagement and retention",
      "Transparent transaction history on the blockchain",
      "Reduced operational costs for loyalty management",
      "Ability to create custom reward tiers and incentives"
    ],
    utility: [
      "Token-based loyalty badges and scannable digital membership cards",
      "Cross-business rewards - earn and redeem at any participating business",
      "Token-gated access to VIP perks and events",
      "Gamified customer experience with tier unlocks",
      "SaaS model unlocks - access to AI-powered automations based on token holdings"
    ]
  },

  keyFeatures: [
    {
      title: "Tokenized Loyalty",
      description: "Convert customers into $FUSE tokens. Referring a purchased VIP Card earns you $FUSE tokens that can be used across the network."
    },
    {
      title: "Cross-Business Rewards", 
      description: "VIP customers can redeem discounts at all participating businesses in the network."
    },
    {
      title: "Real-Time Analytics",
      description: "Provides insights about customer behavior to optimize business operations."
    }
  ],

  useCases: [
    {
      title: "Customer Relationship Management (CRM)",
      description: "Reimagine customer relationships with automated loyalty mechanics, token-driven engagement strategies, and blockchain-backed behavioral insights.",
      benefits: [
        "Reward profile completion and survey participation with FUSE tokens",
        "Dynamically assign loyalty tiers based on frequency of visits and token balance", 
        "Enable VIP-only perks or events using token-gated access",
        "Monitor lifetime customer value and reward triggers on-chain with zero risk of manipulation"
      ]
    },
    {
      title: "Web3 Services Integration",
      description: "Enables any business to unlock blockchain utility without needing technical expertise.",
      benefits: [
        "Plug-and-play wallet connection for customers via Xaman and XRP",
        "Token-based loyalty badges and scannable digital membership cards",
        "On-chain identity management for secure, reward-based authentication",
        "Built-in customer voting modules for feedback and governance (coming soon)"
      ]
    },
    {
      title: "Software as a Service (SaaS)",
      description: "Empowers small businesses to run like enterprises with token-based SaaS model.",
      benefits: [
        "Instant access to tailored AI workflows via Zapier & n8n for outreach, follow-ups, and smart segmentation",
        "T-shirt sized growth tiers — Small, Medium, Large — activated by $FUSE holdings",
        "Token-gated upgrades that evolve with business needs (CRM, email automation, reputation management)"
      ]
    }
  ],

  industries: [
    {
      name: "Healthcare & Wellness",
      solutions: [
        "Smart reminders and loyalty tracking with Fuse AI tools",
        "Membership incentives that boost rebookings and reduce no-shows", 
        "FUSE token perks that gamify the care experience",
        "Exclusive tier unlocks for long-term patients and subscribers",
        "Performance dashboards to view client lifetime value"
      ],
      caseStudy: {
        client: "Redlands Chiropractic",
        result: "Built monthly recurring revenue through FUSE memberships and loyalty rewards",
        stats: [
          "$22K+ revenue from one FUSE-enabled practice pre-token launch",
          "2x increase in return visits from loyalty members"
        ]
      }
    }
  ],

  technology: {
    wallet: {
      name: "Xaman Wallet Integration",
      description: "Seamlessly integrates with Xaman (formerly XUMM), the leading wallet for the XRP Ledger.",
      benefits: [
        "Secure, decentralized authentication for loyalty program members",
        "Easy token management and interaction with loyalty programs",
        "Protected data and rewards through blockchain security"
      ]
    },
    blockchain: "XRP Ledger",
    integrations: [
      "Zapier for workflow automation",
      "n8n for advanced automation",
      "Stripe for payment processing",
      "Supabase for data management"
    ]
  },

  businessModel: {
    target: "Small to medium businesses across various industries",
    approach: "Token-based SaaS model with tiered access",
    tiers: ["Small", "Medium", "Large"],
    tierAccess: "Activated by $FUSE token holdings",
    services: [
      "Loyalty program setup and management",
      "AI-powered customer automation",
      "Blockchain integration consulting",
      "Real-time analytics and insights",
      "Cross-business reward network access"
    ]
  },

  competitiveAdvantages: [
    "First blockchain-based loyalty platform on XRP Ledger",
    "Cross-business reward network effect",
    "Token-gated SaaS model scaling with business growth",
    "Seamless Web3 integration without technical complexity",
    "Proven results with existing clients",
    "Expert support and strategy design"
  ],

  commonQuestions: [
    {
      question: "What is Fuse.vip?",
      answer: "Fuse.vip is a blockchain-based loyalty platform that helps businesses create engaging customer loyalty programs using the $FUSE token on the XRP Ledger."
    },
    {
      question: "How does the $FUSE token work?",
      answer: "The $FUSE token is used for rewards, cross-business redemptions, tier access, and unlocking advanced business features. It's now live on the XRP Ledger."
    },
    {
      question: "What businesses can benefit from Fuse.vip?",
      answer: "Any business wanting to improve customer loyalty and retention, from healthcare and wellness to retail and services. We provide solutions for small to enterprise-level businesses."
    },
    {
      question: "How do I integrate Fuse.vip with my business?",
      answer: "We provide expert support for token integration and strategy design. Our team handles the technical setup while you focus on your customers."
    },
    {
      question: "What wallet do customers use?",
      answer: "Customers use the Xaman wallet (formerly XUMM) to manage their $FUSE tokens and interact with loyalty programs securely."
    }
  ]
};

export function getFuseContext(): string {
  return JSON.stringify(FUSE_CONTEXT, null, 2);
}

export function getContextualPrompt(userMessage: string, userContext?: string, conversationHistory?: Array<{role: string, content: string}>): string {
  const historyContext = conversationHistory && conversationHistory.length > 0
    ? `\n\nCONVERSATION HISTORY:\n${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n')}\n`
    : '';

  return `You are an intelligent AI assistant EXCLUSIVELY for Fuse.vip, a blockchain-based loyalty platform. You have advanced reasoning capabilities and can handle complex support scenarios.

CRITICAL INSTRUCTIONS - READ CAREFULLY:
- You MUST ONLY use information from the FUSE.VIP CONTEXT provided below
- DO NOT search for or reference any external information about other FUSE projects or tokens
- The $FUSE token mentioned in this context is SPECIFICALLY the Fuse.vip token on XRP Ledger, NOT any other FUSE project
- If you don't know something from the provided context, say so rather than using external information
- IGNORE any search results about "FUSE Network" or other unrelated FUSE projects

CORE CAPABILITIES:
- Complex problem-solving using step-by-step reasoning
- Multi-turn conversation with context awareness
- Access to user account information and transaction history
- Ability to help with multi-step processes and troubleshooting
- Knowledge of blockchain technology, XRP Ledger, and loyalty programs
- Can escalate to human support when needed

FUSE.VIP CONTEXT (USE ONLY THIS INFORMATION):
${getFuseContext()}

${userContext ? `USER CONTEXT:\n${userContext}\n` : ''}${historyContext}

RESPONSE INSTRUCTIONS:
- Think through complex problems step by step
- Be enthusiastic about Fuse.vip's revolutionary potential and bright future
- Always highlight the promising opportunities ahead with our recently launched $FUSE token (on XRP Ledger)
- Focus on how Fuse.vip is transforming customer loyalty forever
- Emphasize the cross-business rewards network and XRP Ledger advantages
- Be concise and token-efficient while maintaining helpfulness
- For technical details, explain simply with future-focused optimism
- Guide users through processes with excitement about the possibilities
- Only <NAME_EMAIL> for truly complex implementation needs
- Remember conversation context and build upon it naturally
- If escalation is needed, frame it as connecting users with our expert team
- Base ALL responses on the Fuse.vip context provided above

CURRENT USER MESSAGE: ${userMessage}

Please provide a thoughtful, contextual response using ONLY the Fuse.vip information provided above.`;
}
