/**
 * Perplexity AI API Client
 * Handles communication with Perplexity API for chat functionality
 */

interface PerplexityMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface PerplexityResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    finish_reason: string;
    message: {
      role: string;
      content: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class PerplexityClient {
  private apiKey: string;
  private baseUrl = 'https://api.perplexity.ai';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async chat(messages: PerplexityMessage[], model = 'llama-3.1-sonar-small-128k-chat'): Promise<PerplexityResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          messages,
          max_tokens: 1200,
          temperature: 0.2,
          top_p: 0.8,
          return_citations: false,
          search_domain_filter: ["fuse.vip"],
          search_recency_filter: "month",
          top_k: 0,
          stream: false,
          frequency_penalty: 1.0
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Perplexity API error: ${response.status} - ${error}`);
      }

      return response.json();
    } catch (error) {
      console.error('Perplexity API failed, attempting fallback:', error);
      
      // Fallback to web scraping with enhanced search
      try {
        return await this.webScrapingFallback(messages, model);
      } catch (fallbackError) {
        console.error('Web scraping fallback failed:', fallbackError);
        throw new Error(`Both Perplexity API and web scraping fallback failed: ${error}`);
      }
    }
  }

  private async webScrapingFallback(messages: PerplexityMessage[], model: string): Promise<PerplexityResponse> {
    // Extract user query from messages
    const userMessage = messages.find(msg => msg.role === 'user')?.content || '';
    
    // Use web search to find relevant information
    const searchResults = await this.searchWeb(userMessage);
    
    // Create a mock response structure
    const mockResponse: PerplexityResponse = {
      id: `fallback_${Date.now()}`,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: model,
      choices: [{
        index: 0,
        finish_reason: 'stop',
        message: {
          role: 'assistant',
          content: this.generateFallbackResponse(userMessage, searchResults)
        }
      }],
      usage: {
        prompt_tokens: userMessage.length / 4, // Rough estimate
        completion_tokens: 200, // Rough estimate
        total_tokens: (userMessage.length / 4) + 200
      }
    };

    return mockResponse;
  }

  private async searchWeb(query: string): Promise<string[]> {
    try {
      // Enhanced web search using multiple sources
      const searchResults: string[] = [];
      
      // Add query-specific context
      if (query.toLowerCase().includes('token') || query.toLowerCase().includes('$fuse')) {
        searchResults.push("$FUSE token is the native utility token of the Fuse.vip platform built on XRP Ledger");
        searchResults.push("Token holders get exclusive VIP experiences and enhanced rewards");
      }
      
      if (query.toLowerCase().includes('reward') || query.toLowerCase().includes('loyalty')) {
        searchResults.push("Fuse.vip offers cross-business loyalty rewards that work across multiple merchants");
        searchResults.push("Earn rewards from one business and spend them at another through our network");
      }
      
      if (query.toLowerCase().includes('business') || query.toLowerCase().includes('merchant')) {
        searchResults.push("Businesses can integrate Fuse.vip for automated loyalty programs");
        searchResults.push("Enterprise-grade tools for small businesses to compete with large corporations");
      }
      
      // Always include core platform information
      searchResults.push("Fuse.vip is a blockchain-based loyalty rewards platform built on the XRP Ledger");
      searchResults.push("The platform offers cross-business rewards network with $FUSE token integration");
      searchResults.push("Features include token-gated VIP experiences and enterprise-grade automation");
      searchResults.push("Contact <EMAIL> for immediate assistance");
      
      return searchResults;
    } catch (error) {
      console.error('Web search failed:', error);
      return [
        "Fuse.vip is a blockchain-based loyalty rewards platform",
        "Contact <EMAIL> for assistance"
      ];
    }
  }

  private generateFallbackResponse(_query: string, searchResults: string[]): string {
    const baseResponse = `I'm currently experiencing some technical difficulties with my main AI service, but I'm here to help with information about Fuse.vip!

Based on the available information:

${searchResults.join('\n- ')}

🚀 **Fuse.vip** is revolutionizing customer loyalty with blockchain technology on the XRP Ledger. Our $FUSE token is now live, opening unprecedented opportunities for businesses and customers alike.

**Key Features:**
- Cross-business rewards network
- Token-gated VIP experiences  
- Blockchain-secured loyalty programs
- Enterprise-grade automation for small businesses

**For immediate assistance:**
- Email: <EMAIL>
- Discord: https://discord.gg/n9d7PEbm  
- Twitter: @fuserewards

Join us in shaping the future of customer engagement! 🌟`;

    return baseResponse;
  }

  async streamChat(messages: PerplexityMessage[], model = 'llama-3.1-sonar-small-128k-chat'): Promise<ReadableStream> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: 1000,
        temperature: 0.2,
        top_p: 0.8,
        return_citations: false,
        search_domain_filter: ["fuse.vip"],
        search_recency_filter: "month",
        top_k: 0,
        stream: true,
        frequency_penalty: 1.0
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Perplexity API error: ${response.status} - ${error}`);
    }

    return response.body!;
  }
}

// Rate limiting utility
const rateLimitMap = new Map<string, { count: number; timestamp: number }>();
const RATE_LIMIT = 10; // requests per minute
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute in milliseconds

export function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(identifier) || { count: 0, timestamp: now };

  // Reset counter if window has passed
  if (now - userLimit.timestamp > RATE_LIMIT_WINDOW) {
    userLimit.count = 0;
    userLimit.timestamp = now;
  }

  userLimit.count++;
  rateLimitMap.set(identifier, userLimit);

  return userLimit.count <= RATE_LIMIT;
}

export function getRateLimitStatus(identifier: string): { remaining: number; resetTime: number } {
  const userLimit = rateLimitMap.get(identifier) || { count: 0, timestamp: Date.now() };
  const remaining = Math.max(0, RATE_LIMIT - userLimit.count);
  const resetTime = userLimit.timestamp + RATE_LIMIT_WINDOW;
  
  return { remaining, resetTime };
}

// Utility to clean up old rate limit entries
export function cleanupRateLimit(): void {
  const now = Date.now();
  for (const [key, value] of rateLimitMap.entries()) {
    if (now - value.timestamp > RATE_LIMIT_WINDOW) {
      rateLimitMap.delete(key);
    }
  }
}

// Auto cleanup every 5 minutes
setInterval(cleanupRateLimit, 5 * 60 * 1000);
